<?= $this->extend('templates/adminlte/admindash') ?>
<?= $this->section('content') ?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">Users Management</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Users</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="card card-success card-outline">
            <div class="card-header">
                <h3 class="card-title">User List</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success" data-toggle="modal" data-target="#addUserModal">
                        <i class="fas fa-user-plus"></i> Add User
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table id="usersTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Position</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= esc($user['id']) ?></td>
                            <td><?= esc($user['name']) ?></td>
                            <td><?= esc($user['email']) ?></td>
                            <td><?= esc($user['position']) ?></td>
                            <td>
                                <span class="badge badge-<?= $user['role'] == 'admin' ? 'danger' : 'success' ?>">
                                    <?= esc(ucfirst($user['role'])) ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-<?= $user['status'] ? 'success' : 'danger' ?>">
                                    <?= $user['status'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td>
                            <!-- create view link -->
                            <a href="<?= base_url() ?>users/view/<?= $user['id'] ?>" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i> View Profile
                            </a>

                               <!--  <button type="button" class="btn btn-sm btn-primary"
                                        onclick="editUser(<?= esc(json_encode($user)) ?>)">
                                    <i class="fas fa-edit"></i>
                                </button> -->
                               <!--  <button type="button" class="btn btn-sm btn-danger"
                                        onclick="confirmDelete(<?= $user['id'] ?>, '<?= esc($user['name']) ?>')">
                                    <i class="fas fa-trash"></i>
                                </button> -->
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" role="dialog" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open('users/add_user') ?>
            <div class="modal-body">
                <div class="form-group">
                    <label>File Number</label>
                    <input type="text" class="form-control" name="fileno" id="fileno" required>
                    <small id="filenoFeedback" class="form-text"></small>
                </div>
                <div class="form-group">
                    <label>Full Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>
              <!--   <div class="form-group">
                    <label>Username</label>
                    <input type="text" class="form-control" name="username" required>
                </div> -->
                <div class="form-group">
                    <label>Password</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <div class="form-group">
                    <label>Position</label>
                    <input type="text" class="form-control" name="position" required>
                </div>
                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" required>
                        <option value="admin">Admin</option>
                        <option value="supervisor">Supervisor</option>
                        <option value="user">User</option>
                        <option value="guest">Guest</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Add User</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open('users/update_user') ?>
            <input type="hidden" name="id" id="edit_id">
            <div class="modal-body">
                <div class="form-group">
                    <label>File Number</label>
                    <input type="text" class="form-control" name="fileno" id="edit_fileno" required>
                    <small id="edit_filenoFeedback" class="form-text"></small>
                </div>
                <div class="form-group">
                    <label>Full Name</label>
                    <input type="text" class="form-control" name="name" id="edit_name" required>
                </div>
                <div class="form-group">
                    <label>Position</label>
                    <input type="text" class="form-control" name="position" id="edit_position" required>
                </div>
                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" id="edit_role" required>
                        <option value="admin">Admin</option>
                        <option value="supervisor">Supervisor</option>
                        <option value="user">User</option>
                        <option value="guest">Guest</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Status</label>
                    <select class="form-control" name="status" id="edit_status">
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>New Password (leave blank to keep current)</label>
                    <input type="password" class="form-control" name="password">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Update User</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#usersTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "buttons": [ "excel", "pdf", "print"]
    }).buttons().container().appendTo('#usersTable_wrapper .col-md-6:eq(0)');

    // File number validation
    let filenoTimer;
    $('#fileno').on('keyup', function() {
        clearTimeout(filenoTimer);
        const fileno = $(this).val();
        const feedbackElement = $('#filenoFeedback');
        const submitButton = $(this).closest('form').find('button[type="submit"]');

        // Clear feedback if input is empty
        if (!fileno) {
            feedbackElement.html('').removeClass('text-success text-danger');
            submitButton.prop('disabled', false);
            return;
        }

        // Add loading indicator
        feedbackElement.html('<i class="fas fa-spinner fa-spin"></i> Checking availability...').removeClass('text-success text-danger');

        // Delay the check to prevent too many requests
        filenoTimer = setTimeout(function() {
            $.ajax({
                url: '<?= base_url() ?>users/check_fileno',
                type: 'POST',
                data: { fileno: fileno },
                success: function(response) {
                    if (response.exists) {
                        feedbackElement.html('<i class="fas fa-times-circle"></i> ' + response.message)
                                     .addClass('text-danger')
                                     .removeClass('text-success');
                        submitButton.prop('disabled', true);
                    } else {
                        feedbackElement.html('<i class="fas fa-check-circle"></i> ' + response.message)
                                     .addClass('text-success')
                                     .removeClass('text-danger');
                        submitButton.prop('disabled', false);
                    }
                },
                error: function() {
                    feedbackElement.html('<i class="fas fa-exclamation-circle"></i> Error checking file number')
                                 .addClass('text-danger')
                                 .removeClass('text-success');
                    submitButton.prop('disabled', true);
                }
            });
        }, 500); // Wait 500ms after last keypress before checking
    });
});

function editUser(user) {
    $('#edit_id').val(user.id);
    $('#edit_email').val(user.email);
    $('#edit_phone').val(user.phone);
    $('#edit_name').val(user.name);
    $('#edit_position').val(user.position);
    $('#edit_role').val(user.role);
    $('#edit_status').val(user.status);
    $('#editUserModal').modal('show');

    // Add email validation for edit form
    let editEmailTimer;
    $('#edit_email').on('keyup', function() {
        clearTimeout(editEmailTimer);
        const email = $(this).val();
        const originalEmail = user.email;
        const feedbackElement = $('#edit_emailFeedback');
        const submitButton = $(this).closest('form').find('button[type="submit"]');

        // Skip validation if email hasn't changed
        if (email === originalEmail) {
            feedbackElement.html('').removeClass('text-success text-danger');
            submitButton.prop('disabled', false);
            return;
        }

        // Clear feedback if input is empty
        if (!email) {
            feedbackElement.html('').removeClass('text-success text-danger');
            submitButton.prop('disabled', false);
            return;
        }

        // Add loading indicator
        feedbackElement.html('<i class="fas fa-spinner fa-spin"></i> Checking availability...').removeClass('text-success text-danger');

        // Delay the check to prevent too many requests
        editEmailTimer = setTimeout(function() {
            $.ajax({
                url: '<?= base_url() ?>users/check_email',
                type: 'POST',
                data: { email: email },
                success: function(response) {
                    if (response.exists) {
                        feedbackElement.html('<i class="fas fa-times-circle"></i> ' + response.message)
                                     .addClass('text-danger')
                                     .removeClass('text-success');
                        submitButton.prop('disabled', true);
                    } else {
                        feedbackElement.html('<i class="fas fa-check-circle"></i> ' + response.message)
                                     .addClass('text-success')
                                     .removeClass('text-danger');
                        submitButton.prop('disabled', false);
                    }
                },
                error: function() {
                    feedbackElement.html('<i class="fas fa-exclamation-circle"></i> Error checking email')
                                 .addClass('text-danger')
                                 .removeClass('text-success');
                    submitButton.prop('disabled', true);
                }
            });
        }, 500);
    });
}

function confirmDelete(id, name) {
    if (confirm('Are you sure you want to delete this user: ' + name + '?')) {
        window.location.href = '<?= base_url() ?>users/delete_user/' + id;
    }
}
</script>

<style>
.modal-header {
    color: white;
}
.badge {
    font-size: 0.9em;
    padding: 0.5em 1em;
}
#emailFeedback, #edit_emailFeedback {
    margin-top: 0.25rem;
    font-size: 80%;
}
.text-success {
    color: #28a745 !important;
}
.text-danger {
    color: #dc3545 !important;
}
</style>

<?= $this->endSection() ?>
